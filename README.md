# PS图层智能分离器

一个基于Adobe UXP框架开发的Photoshop插件，提供AI驱动的图片文字提取和元素背景分离功能。

## 功能特性

### 🔍 智能文字分层
- **多语言OCR识别**：支持中文、英文、日文、韩文、俄文等多种语言
- **保持原始属性**：提取的文字保持原图的大小、位置、颜色、角度等视觉属性
- **高精度识别**：基于Tesseract.js引擎，提供快速、平衡、精确三种识别模式
- **自动图层创建**：将识别的文字自动创建为独立的可编辑文字图层

### ✂️ 智能元素分离
- **AI背景分离**：使用先进的图像分割技术，智能分离前景元素和背景
- **多种分离模式**：支持自动分离、人物分离、物体分离三种模式
- **透明背景处理**：自动创建透明背景的新图层，便于后续编辑
- **质量可调节**：提供草稿、标准、高质量三种处理质量选项

### ⚡ 批量处理
- **多图层同时处理**：支持选择多个图层进行批量文字提取或元素分离
- **进度实时显示**：处理过程中显示详细的进度信息和状态
- **结果统计报告**：处理完成后提供详细的结果统计和成功率报告

## 安装方法

### 方法一：直接安装到Photoshop扩展目录

1. **定位扩展目录**：
   ```
   Windows: C:\Program Files\Adobe\Adobe Photoshop 2025\Required\CEP\extensions\
   macOS: /Applications/Adobe Photoshop 2025/Required/CEP/extensions/
   ```

2. **复制插件文件**：
   - 将整个插件文件夹复制到上述扩展目录中
   - 确保文件夹名称为 `ps图片转` 或其他有意义的名称

3. **重启Photoshop**：
   - 完全关闭Photoshop
   - 重新启动Photoshop

4. **启用插件**：
   - 在Photoshop中，转到 `窗口` > `扩展功能` > `图层智能分离器`

### 方法二：使用UXP Developer Tool安装

1. **下载UXP Developer Tool**：
   - 从Adobe官网下载并安装UXP Developer Tool

2. **加载插件**：
   - 打开UXP Developer Tool
   - 点击"Add Plugin"
   - 选择插件的manifest.json文件
   - 点击"Load"

3. **在Photoshop中使用**：
   - 确保Photoshop正在运行
   - 在UXP Developer Tool中点击插件旁的"..."按钮
   - 选择"Load in Photoshop"

## 使用指南

### 文字分层操作

1. **选择图层**：在Photoshop中选择包含文字的图层
2. **选择识别语言**：在插件界面中选择合适的OCR识别语言
3. **设置识别精度**：根据需要选择快速、平衡或精确模式
4. **开始提取**：点击"提取文字分层"按钮
5. **查看结果**：等待处理完成，查看创建的文字图层

### 元素分离操作

1. **选择图层**：选择需要分离背景的图层
2. **选择分离模式**：
   - **自动分离**：适用于大多数情况
   - **人物分离**：专门针对人物照片优化
   - **物体分离**：适用于产品图片等物体分离
3. **设置处理质量**：选择草稿、标准或高质量
4. **开始分离**：点击"分离元素背景"按钮
5. **查看结果**：处理完成后会创建新的透明背景图层

### 批量处理操作

1. **选择多个图层**：按住Ctrl/Cmd键选择多个图层
2. **选择处理类型**：勾选"批量文字分层"和/或"批量元素分离"
3. **开始批量处理**：点击"批量处理选中图层"按钮
4. **监控进度**：查看处理进度和每个图层的处理状态
5. **查看报告**：处理完成后查看详细的结果报告

## 高级设置

### OCR设置
- **识别精度**：
  - 快速：处理速度快，适合预览
  - 平衡：速度和精度的平衡选择
  - 精确：最高精度，处理时间较长

### 分离设置
- **处理质量**：
  - 草稿：快速处理，适合预览效果
  - 标准：推荐设置，平衡质量和速度
  - 高质量：最佳效果，处理时间较长

### 其他选项
- **保留原始图层**：处理后是否保留原始图层（默认开启）

## 技术要求

### 系统要求
- **Photoshop版本**：Adobe Photoshop 2024或更高版本
- **操作系统**：Windows 10/11 或 macOS 10.15+
- **内存**：建议8GB以上RAM
- **网络连接**：某些功能需要网络连接（如Remove.bg API）

### 支持的图片格式
- **输入格式**：JPEG, PNG, TIFF, PSD等Photoshop支持的格式
- **输出格式**：创建的图层为Photoshop原生格式

## 常见问题

### Q: 文字识别不准确怎么办？
A: 
1. 尝试调整OCR精度设置为"精确"
2. 确保图片中的文字清晰可见
3. 选择正确的识别语言
4. 对于复杂背景的文字，可以先进行图像预处理

### Q: 元素分离效果不理想？
A: 
1. 尝试不同的分离模式（自动/人物/物体）
2. 调整处理质量为"高质量"
3. 确保前景元素与背景有足够的对比度
4. 对于复杂场景，可能需要手动调整

### Q: 插件无法加载？
A: 
1. 确认Photoshop版本兼容性
2. 检查插件文件是否完整
3. 重启Photoshop
4. 检查系统权限设置

### Q: 处理速度很慢？
A: 
1. 降低处理质量设置
2. 关闭其他占用内存的程序
3. 确保网络连接稳定（如使用在线API）
4. 考虑分批处理大量图层

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持多语言OCR文字识别
- 支持AI元素背景分离
- 支持批量处理功能
- 提供完整的用户界面

## 技术支持

如果您在使用过程中遇到问题，请：

1. 查看本README文件的常见问题部分
2. 检查Photoshop控制台是否有错误信息
3. 确保所有依赖项已正确安装
4. 联系技术支持获取帮助

## 许可证

本插件遵循MIT许可证。详细信息请查看LICENSE文件。

## 贡献

欢迎提交问题报告和功能建议。如果您想贡献代码，请：

1. Fork本项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

---

**注意**：本插件使用了第三方服务（如Tesseract.js、Remove.bg API等），请确保遵守相关服务的使用条款。
