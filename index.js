/**
 * PS图层智能分离器 - 主要逻辑文件
 * 基于Adobe UXP框架开发
 */

// 导入必要的UXP模块
const { app, core } = require('photoshop');
const fs = require('uxp').storage.localFileSystem;

// 导入自定义类（这些类在其他文件中定义）
// 在实际运行时，这些类会通过script标签加载

// 全局变量
let isProcessing = false;
let currentOperation = null;

/**
 * 主应用类 - 管理整个插件的生命周期
 */
class LayerSeparatorApp {
    constructor() {
        this.textExtractor = new TextExtractor();
        this.imageSegmentation = new ImageSegmentation();
        this.uiController = new UIController();
        this.psApiHelper = new PSAPIHelper();

        this.init();
    }

    /**
     * 初始化应用
     */
    async init() {
        try {
            await this.setupEventListeners();
            await this.loadSettings();
            this.uiController.showMessage('插件已就绪', 'success');
        } catch (error) {
            console.error('初始化失败:', error);
            this.uiController.showError('插件初始化失败: ' + error.message);
        }
    }

    /**
     * 设置事件监听器
     */
    async setupEventListeners() {
        // 文字分层按钮
        document.getElementById('extractTextBtn').addEventListener('click', () => {
            this.handleTextExtraction();
        });

        // 元素分层按钮
        document.getElementById('separateElementBtn').addEventListener('click', () => {
            this.handleElementSeparation();
        });

        // 批量处理按钮
        document.getElementById('batchProcessBtn').addEventListener('click', () => {
            this.handleBatchProcess();
        });

        // 取消按钮
        document.getElementById('cancelBtn').addEventListener('click', () => {
            this.cancelCurrentOperation();
        });

        // 关闭结果按钮
        document.getElementById('closeResultBtn').addEventListener('click', () => {
            this.uiController.hideResult();
        });
    }

    /**
     * 处理文字提取
     */
    async handleTextExtraction() {
        if (isProcessing) return;

        try {
            isProcessing = true;
            this.uiController.showProgress('正在提取文字...', 0);

            const activeLayer = await this.psApiHelper.getActiveLayer();
            if (!activeLayer) {
                throw new Error('请选择一个图层');
            }

            const language = document.getElementById('ocrLanguage').value;
            const accuracy = document.getElementById('ocrAccuracy').value;

            // 执行文字提取
            const result = await this.textExtractor.extractFromLayer(activeLayer, {
                language,
                accuracy,
                onProgress: (progress) => {
                    this.uiController.updateProgress(progress);
                }
            });

            this.uiController.showResult('文字提取完成', result);

        } catch (error) {
            console.error('文字提取失败:', error);
            this.uiController.showError('文字提取失败: ' + error.message);
        } finally {
            isProcessing = false;
            this.uiController.hideProgress();
        }
    }

    /**
     * 处理元素分离
     */
    async handleElementSeparation() {
        if (isProcessing) return;

        try {
            isProcessing = true;
            this.uiController.showProgress('正在分离元素...', 0);

            const activeLayer = await this.psApiHelper.getActiveLayer();
            if (!activeLayer) {
                throw new Error('请选择一个图层');
            }

            const segmentMode = document.querySelector('input[name="segmentMode"]:checked').value;
            const quality = document.getElementById('segmentQuality').value;

            // 执行元素分离
            const result = await this.imageSegmentation.separateElement(activeLayer, {
                mode: segmentMode,
                quality,
                onProgress: (progress) => {
                    this.uiController.updateProgress(progress);
                }
            });

            this.uiController.showResult('元素分离完成', result);

        } catch (error) {
            console.error('元素分离失败:', error);
            this.uiController.showError('元素分离失败: ' + error.message);
        } finally {
            isProcessing = false;
            this.uiController.hideProgress();
        }
    }

    /**
     * 处理批量操作
     */
    async handleBatchProcess() {
        if (isProcessing) return;

        try {
            isProcessing = true;
            const batchText = document.getElementById('batchText').checked;
            const batchElement = document.getElementById('batchElement').checked;

            if (!batchText && !batchElement) {
                throw new Error('请选择至少一种批量处理类型');
            }

            this.uiController.showProgress('正在批量处理...', 0);

            const selectedLayers = await this.psApiHelper.getSelectedLayers();
            if (selectedLayers.length === 0) {
                throw new Error('请选择要处理的图层');
            }

            const results = [];
            const totalLayers = selectedLayers.length;

            for (let i = 0; i < selectedLayers.length; i++) {
                const layer = selectedLayers[i];
                const progress = (i / totalLayers) * 100;

                this.uiController.updateProgress(progress, `处理图层 ${i + 1}/${totalLayers}: ${layer.name}`);

                if (batchText) {
                    const textResult = await this.textExtractor.extractFromLayer(layer);
                    results.push({ type: 'text', layer: layer.name, result: textResult });
                }

                if (batchElement) {
                    const elementResult = await this.imageSegmentation.separateElement(layer);
                    results.push({ type: 'element', layer: layer.name, result: elementResult });
                }
            }

            this.uiController.showResult('批量处理完成', {
                totalProcessed: selectedLayers.length,
                results: results
            });

        } catch (error) {
            console.error('批量处理失败:', error);
            this.uiController.showError('批量处理失败: ' + error.message);
        } finally {
            isProcessing = false;
            this.uiController.hideProgress();
        }
    }

    /**
     * 取消当前操作
     */
    cancelCurrentOperation() {
        if (currentOperation) {
            currentOperation.cancel();
            currentOperation = null;
        }
        isProcessing = false;
        this.uiController.hideProgress();
        this.uiController.showMessage('操作已取消', 'warning');
    }

    /**
     * 加载设置
     */
    async loadSettings() {
        try {
            // 从本地存储加载用户设置
            const settings = await this.loadUserSettings();
            if (settings) {
                this.applySettings(settings);
            }
        } catch (error) {
            console.warn('加载设置失败:', error);
        }
    }

    /**
     * 应用设置
     */
    applySettings(settings) {
        if (settings.ocrLanguage) {
            document.getElementById('ocrLanguage').value = settings.ocrLanguage;
        }
        if (settings.ocrAccuracy) {
            document.getElementById('ocrAccuracy').value = settings.ocrAccuracy;
        }
        if (settings.segmentQuality) {
            document.getElementById('segmentQuality').value = settings.segmentQuality;
        }
        if (settings.preserveOriginal !== undefined) {
            document.getElementById('preserveOriginal').checked = settings.preserveOriginal;
        }
    }

    /**
     * 加载用户设置
     */
    async loadUserSettings() {
        try {
            const pluginFolder = await fs.getPluginFolder();
            const settingsFile = await pluginFolder.getEntry('settings.json');
            const content = await settingsFile.read();
            return JSON.parse(content);
        } catch (error) {
            return null; // 设置文件不存在或读取失败
        }
    }

    /**
     * 保存用户设置
     */
    async saveUserSettings() {
        try {
            const settings = {
                ocrLanguage: document.getElementById('ocrLanguage').value,
                ocrAccuracy: document.getElementById('ocrAccuracy').value,
                segmentQuality: document.getElementById('segmentQuality').value,
                preserveOriginal: document.getElementById('preserveOriginal').checked
            };

            const pluginFolder = await fs.getPluginFolder();
            const settingsFile = await pluginFolder.createFile('settings.json', { overwrite: true });
            await settingsFile.write(JSON.stringify(settings, null, 2));
        } catch (error) {
            console.warn('保存设置失败:', error);
        }
    }
}

// 当DOM加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.layerSeparatorApp = new LayerSeparatorApp();
});

// 在窗口关闭前保存设置
window.addEventListener('beforeunload', () => {
    if (window.layerSeparatorApp) {
        window.layerSeparatorApp.saveUserSettings();
    }
});
