/**
 * PS图层智能分离器 - CEP版本主要逻辑文件
 * 基于Adobe CEP框架开发
 */

// 全局变量
let isProcessing = false;
let currentOperation = null;

/**
 * CEP版本的主应用类
 */
class LayerSeparatorCEPApp {
    constructor() {
        this.textExtractor = new TextExtractor();
        this.imageSegmentation = new ImageSegmentation();
        this.uiController = new UIController();
        this.cepApiHelper = new CEPAPIHelper();
        
        this.init();
    }

    /**
     * 初始化应用
     */
    async init() {
        try {
            // 等待CEP接口准备就绪
            await this.waitForCEPReady();
            
            await this.setupEventListeners();
            await this.loadSettings();
            
            this.uiController.showMessage('插件已就绪', 'success');
            
            // 获取扩展信息
            const extInfo = this.cepApiHelper.getExtensionInfo();
            console.log('扩展信息:', extInfo);
            
        } catch (error) {
            console.error('初始化失败:', error);
            this.uiController.showError('插件初始化失败: ' + error.message);
        }
    }

    /**
     * 等待CEP接口准备就绪
     */
    async waitForCEPReady() {
        return new Promise((resolve) => {
            if (typeof CSInterface !== 'undefined') {
                resolve();
            } else {
                const checkInterval = setInterval(() => {
                    if (typeof CSInterface !== 'undefined') {
                        clearInterval(checkInterval);
                        resolve();
                    }
                }, 100);
            }
        });
    }

    /**
     * 设置事件监听器
     */
    async setupEventListeners() {
        // 文字分层按钮
        document.getElementById('extractTextBtn').addEventListener('click', () => {
            this.handleTextExtraction();
        });

        // 元素分层按钮
        document.getElementById('separateElementBtn').addEventListener('click', () => {
            this.handleElementSeparation();
        });

        // 批量处理按钮
        document.getElementById('batchProcessBtn').addEventListener('click', () => {
            this.handleBatchProcess();
        });

        // 取消按钮
        document.getElementById('cancelBtn').addEventListener('click', () => {
            this.cancelCurrentOperation();
        });

        // 关闭结果按钮
        document.getElementById('closeResultBtn').addEventListener('click', () => {
            this.uiController.hideResult();
        });

        // 监听Photoshop事件
        this.cepApiHelper.addEventListener('documentChanged', () => {
            console.log('文档已更改');
        });
    }

    /**
     * 处理文字提取
     */
    async handleTextExtraction() {
        if (isProcessing) return;

        try {
            isProcessing = true;
            this.uiController.showProgress('正在提取文字...', 0);
            this.uiController.disableAllButtons();

            // 获取当前活动图层
            const activeLayer = await this.cepApiHelper.getActiveLayer();
            if (!activeLayer) {
                throw new Error('请选择一个图层');
            }

            const language = document.getElementById('ocrLanguage').value;
            const accuracy = document.getElementById('ocrAccuracy').value;

            this.uiController.updateProgress(20, '导出图层图像...');

            // 导出图层图像
            const imageData = await this.cepApiHelper.exportLayerAsImage(activeLayer.name);

            this.uiController.updateProgress(40, '执行OCR识别...');

            // 执行文字提取
            const result = await this.textExtractor.extractFromImageData(imageData, {
                language,
                accuracy,
                onProgress: (progress, message) => {
                    this.uiController.updateProgress(40 + progress * 0.4, message);
                }
            });

            this.uiController.updateProgress(80, '创建文字图层...');

            // 创建文字图层
            const textLayers = await this.createTextLayersFromOCR(result, activeLayer);

            this.uiController.updateProgress(100, '文字提取完成');

            this.uiController.showResult('文字提取完成', {
                success: true,
                extractedTexts: result.data.words.length,
                createdLayers: textLayers.length,
                textLayers: textLayers,
                confidence: this.calculateAverageConfidence(result)
            });

        } catch (error) {
            console.error('文字提取失败:', error);
            this.uiController.showError('文字提取失败: ' + error.message);
        } finally {
            isProcessing = false;
            this.uiController.hideProgress();
            this.uiController.enableAllButtons();
        }
    }

    /**
     * 从OCR结果创建文字图层
     */
    async createTextLayersFromOCR(ocrResult, originalLayer) {
        const textLayers = [];
        const words = ocrResult.data.words;

        try {
            for (const word of words) {
                if (word.confidence < 60) continue; // 跳过置信度低的文字

                const { text, bbox, confidence } = word;
                
                // 计算文字位置和大小
                const x = bbox.x0 + originalLayer.bounds.left;
                const y = bbox.y0 + originalLayer.bounds.top;
                const height = bbox.y1 - bbox.y0;
                const fontSize = Math.max(12, height * 0.8);

                // 创建文字图层
                const textLayerData = await this.cepApiHelper.createTextLayer(text, {
                    x: x,
                    y: y,
                    fontSize: fontSize,
                    fontFamily: this.selectAppropriateFont(text),
                    color: { r: 0, g: 0, b: 0 }, // 默认黑色
                    name: `提取文字: ${text.substring(0, 10)}`
                });

                textLayers.push({
                    ...textLayerData,
                    text: text,
                    confidence: confidence,
                    position: { x, y },
                    size: fontSize
                });
            }

            return textLayers;

        } catch (error) {
            throw new Error('创建文字图层失败: ' + error.message);
        }
    }

    /**
     * 处理元素分离
     */
    async handleElementSeparation() {
        if (isProcessing) return;

        try {
            isProcessing = true;
            this.uiController.showProgress('正在分离元素...', 0);
            this.uiController.disableAllButtons();

            const activeLayer = await this.cepApiHelper.getActiveLayer();
            if (!activeLayer) {
                throw new Error('请选择一个图层');
            }

            const segmentMode = document.querySelector('input[name="segmentMode"]:checked').value;
            const quality = document.getElementById('segmentQuality').value;

            this.uiController.updateProgress(20, '导出图层图像...');

            // 导出图层图像
            const imageData = await this.cepApiHelper.exportLayerAsImage(activeLayer.name);

            this.uiController.updateProgress(40, '执行背景分离...');

            // 执行元素分离
            const maskData = await this.imageSegmentation.performSegmentation(imageData, segmentMode, quality);

            this.uiController.updateProgress(70, '创建透明图层...');

            // 复制图层
            const newLayerName = activeLayer.name + '_透明背景';
            await this.cepApiHelper.duplicateLayer(activeLayer.name, newLayerName);

            // 应用蒙版
            await this.cepApiHelper.applyMaskToLayer(newLayerName, maskData);

            this.uiController.updateProgress(100, '元素分离完成');

            this.uiController.showResult('元素分离完成', {
                success: true,
                originalLayer: activeLayer.name,
                newLayer: newLayerName,
                mode: segmentMode,
                quality: quality
            });

        } catch (error) {
            console.error('元素分离失败:', error);
            this.uiController.showError('元素分离失败: ' + error.message);
        } finally {
            isProcessing = false;
            this.uiController.hideProgress();
            this.uiController.enableAllButtons();
        }
    }

    /**
     * 处理批量操作
     */
    async handleBatchProcess() {
        if (isProcessing) return;

        try {
            isProcessing = true;
            const batchText = document.getElementById('batchText').checked;
            const batchElement = document.getElementById('batchElement').checked;

            if (!batchText && !batchElement) {
                throw new Error('请选择至少一种批量处理类型');
            }

            this.uiController.showProgress('正在批量处理...', 0);
            this.uiController.disableAllButtons();

            const selectedLayers = await this.cepApiHelper.getSelectedLayers();
            if (selectedLayers.length === 0) {
                throw new Error('请选择要处理的图层');
            }

            const results = [];
            const totalLayers = selectedLayers.length;

            for (let i = 0; i < selectedLayers.length; i++) {
                const layer = selectedLayers[i];
                const progress = (i / totalLayers) * 100;
                
                this.uiController.updateProgress(progress, `处理图层 ${i + 1}/${totalLayers}: ${layer.name}`);

                try {
                    if (batchText) {
                        const imageData = await this.cepApiHelper.exportLayerAsImage(layer.name);
                        const textResult = await this.textExtractor.extractFromImageData(imageData);
                        const textLayers = await this.createTextLayersFromOCR(textResult, layer);
                        results.push({ 
                            type: 'text', 
                            layer: layer.name, 
                            result: { success: true, createdLayers: textLayers.length } 
                        });
                    }

                    if (batchElement) {
                        const imageData = await this.cepApiHelper.exportLayerAsImage(layer.name);
                        const maskData = await this.imageSegmentation.performSegmentation(imageData, 'auto', 'standard');
                        const newLayerName = layer.name + '_透明背景';
                        await this.cepApiHelper.duplicateLayer(layer.name, newLayerName);
                        await this.cepApiHelper.applyMaskToLayer(newLayerName, maskData);
                        results.push({ 
                            type: 'element', 
                            layer: layer.name, 
                            result: { success: true, newLayer: newLayerName } 
                        });
                    }
                } catch (error) {
                    results.push({ 
                        type: batchText ? 'text' : 'element', 
                        layer: layer.name, 
                        result: { success: false, error: error.message } 
                    });
                }
            }

            this.uiController.showResult('批量处理完成', {
                success: true,
                totalProcessed: selectedLayers.length,
                results: results
            });

        } catch (error) {
            console.error('批量处理失败:', error);
            this.uiController.showError('批量处理失败: ' + error.message);
        } finally {
            isProcessing = false;
            this.uiController.hideProgress();
            this.uiController.enableAllButtons();
        }
    }

    /**
     * 取消当前操作
     */
    cancelCurrentOperation() {
        if (currentOperation) {
            currentOperation.cancel();
            currentOperation = null;
        }
        isProcessing = false;
        this.uiController.hideProgress();
        this.uiController.enableAllButtons();
        this.uiController.showMessage('操作已取消', 'warning');
    }

    /**
     * 选择合适的字体
     */
    selectAppropriateFont(text) {
        const hasChinese = /[\u4e00-\u9fff]/.test(text);
        const hasJapanese = /[\u3040-\u309f\u30a0-\u30ff]/.test(text);
        const hasKorean = /[\uac00-\ud7af]/.test(text);

        if (hasChinese) {
            return 'SimSun';
        } else if (hasJapanese) {
            return 'MS Gothic';
        } else if (hasKorean) {
            return 'Malgun Gothic';
        } else {
            return 'Arial';
        }
    }

    /**
     * 计算平均置信度
     */
    calculateAverageConfidence(ocrResult) {
        const words = ocrResult.data.words;
        if (words.length === 0) return 0;

        const totalConfidence = words.reduce((sum, word) => sum + word.confidence, 0);
        return Math.round(totalConfidence / words.length);
    }

    /**
     * 加载设置
     */
    async loadSettings() {
        try {
            // 从本地存储加载用户设置
            const settings = this.loadUserSettings();
            if (settings) {
                this.applySettings(settings);
            }
        } catch (error) {
            console.warn('加载设置失败:', error);
        }
    }

    /**
     * 应用设置
     */
    applySettings(settings) {
        if (settings.ocrLanguage) {
            document.getElementById('ocrLanguage').value = settings.ocrLanguage;
        }
        if (settings.ocrAccuracy) {
            document.getElementById('ocrAccuracy').value = settings.ocrAccuracy;
        }
        if (settings.segmentQuality) {
            document.getElementById('segmentQuality').value = settings.segmentQuality;
        }
        if (settings.preserveOriginal !== undefined) {
            document.getElementById('preserveOriginal').checked = settings.preserveOriginal;
        }
    }

    /**
     * 加载用户设置
     */
    loadUserSettings() {
        try {
            const settings = localStorage.getItem('ps-layer-separator-settings');
            return settings ? JSON.parse(settings) : null;
        } catch (error) {
            return null;
        }
    }

    /**
     * 保存用户设置
     */
    saveUserSettings() {
        try {
            const settings = {
                ocrLanguage: document.getElementById('ocrLanguage').value,
                ocrAccuracy: document.getElementById('ocrAccuracy').value,
                segmentQuality: document.getElementById('segmentQuality').value,
                preserveOriginal: document.getElementById('preserveOriginal').checked
            };

            localStorage.setItem('ps-layer-separator-settings', JSON.stringify(settings));
        } catch (error) {
            console.warn('保存设置失败:', error);
        }
    }

    /**
     * 清理资源
     */
    async cleanup() {
        try {
            await this.textExtractor.cleanup();
            await this.cepApiHelper.cleanupTempFiles();
        } catch (error) {
            console.warn('清理资源失败:', error);
        }
    }
}

// 当DOM加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.layerSeparatorApp = new LayerSeparatorCEPApp();
});

// 在窗口关闭前保存设置和清理资源
window.addEventListener('beforeunload', () => {
    if (window.layerSeparatorApp) {
        window.layerSeparatorApp.saveUserSettings();
        window.layerSeparatorApp.cleanup();
    }
});
