/* Adobe UXP 插件样式 */
:root {
    --primary-color: #0066cc;
    --secondary-color: #666666;
    --success-color: #00aa00;
    --warning-color: #ff9900;
    --error-color: #cc0000;
    --background-color: #f5f5f5;
    --panel-background: #ffffff;
    --border-color: #dddddd;
    --text-color: #333333;
    --text-secondary: #666666;
    --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --border-radius: 6px;
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --font-size-sm: 12px;
    --font-size-md: 14px;
    --font-size-lg: 16px;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: var(--font-size-md);
    color: var(--text-color);
    background-color: var(--background-color);
    line-height: 1.5;
}

.container {
    max-width: 100%;
    padding: var(--spacing-md);
    min-height: 100vh;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: linear-gradient(135deg, var(--primary-color), #0088ff);
    color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.header h1 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.subtitle {
    font-size: var(--font-size-sm);
    opacity: 0.9;
}

/* 主要内容区域 */
.main-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

/* 功能区块样式 */
.feature-section {
    background: var(--panel-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    box-shadow: var(--shadow);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.feature-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.feature-header h2 {
    font-size: var(--font-size-lg);
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.feature-header p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-md);
}

.feature-controls {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

/* 按钮样式 */
.primary-btn, .secondary-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius);
    font-size: var(--font-size-md);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 44px;
}

.primary-btn {
    background: var(--primary-color);
    color: white;
}

.primary-btn:hover {
    background: #0052a3;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 102, 204, 0.3);
}

.secondary-btn {
    background: var(--secondary-color);
    color: white;
}

.secondary-btn:hover {
    background: #555555;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(102, 102, 102, 0.3);
}

.btn-icon {
    font-size: var(--font-size-lg);
}

/* 表单控件样式 */
.language-selector, .segmentation-options, .batch-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.language-selector label {
    font-weight: 500;
    color: var(--text-color);
}

.language-selector select {
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: var(--font-size-md);
    background: white;
}

.segmentation-options, .batch-options {
    flex-direction: row;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.segmentation-options label, .batch-options label {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    cursor: pointer;
}

/* 进度条样式 */
.progress-container {
    background: var(--panel-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    margin-top: var(--spacing-md);
    box-shadow: var(--shadow);
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.progress-header h3 {
    color: var(--primary-color);
    font-size: var(--font-size-md);
}

.cancel-btn, .close-btn {
    background: var(--error-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    padding: var(--spacing-xs) var(--spacing-sm);
    cursor: pointer;
    font-size: var(--font-size-sm);
}

.cancel-btn:hover, .close-btn:hover {
    background: #aa0000;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--success-color));
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    text-align: center;
}

/* 结果显示样式 */
.result-container {
    background: var(--panel-background);
    border: 1px solid var(--success-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    margin-top: var(--spacing-md);
    box-shadow: var(--shadow);
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.result-header h3 {
    color: var(--success-color);
    font-size: var(--font-size-md);
}

.result-content {
    font-size: var(--font-size-sm);
    line-height: 1.6;
}

/* 设置区域样式 */
.settings {
    margin-top: var(--spacing-lg);
    background: var(--panel-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.settings details summary {
    padding: var(--spacing-md);
    background: var(--background-color);
    cursor: pointer;
    font-weight: 500;
    border-bottom: 1px solid var(--border-color);
}

.settings details[open] summary {
    border-bottom: 1px solid var(--border-color);
}

.settings-content {
    padding: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.setting-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.setting-item label {
    font-weight: 500;
    font-size: var(--font-size-sm);
}

.setting-item select {
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
}

.setting-item label input[type="checkbox"] {
    margin-right: var(--spacing-xs);
}

/* 工具类 */
.hidden {
    display: none !important;
}

.disabled {
    opacity: 0.6;
    pointer-events: none;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .container {
        padding: var(--spacing-sm);
    }
    
    .feature-controls {
        gap: var(--spacing-sm);
    }
    
    .segmentation-options, .batch-options {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.feature-section {
    animation: fadeIn 0.3s ease-out;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--background-color);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}
