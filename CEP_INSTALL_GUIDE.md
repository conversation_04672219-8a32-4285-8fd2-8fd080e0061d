# CEP扩展安装指南

## 快速安装步骤

### 1. 准备文件
确保您的插件文件夹包含以下文件：
```
ps图片转/
├── manifest_cep.xml       # CEP扩展配置文件（重要！）
├── index.html            # 主界面文件
├── index_cep.js          # CEP版本主逻辑
├── styles.css            # 样式文件
├── jsx/host.jsx          # Photoshop脚本文件
├── lib/                  # 功能库文件夹
└── assets/               # 资源文件夹
```

### 2. 重命名配置文件
**重要步骤：** 将 `manifest_cep.xml` 重命名为 `CSXS/manifest.xml`

```bash
# 在插件文件夹中创建CSXS目录
mkdir CSXS

# 将manifest_cep.xml移动并重命名
move manifest_cep.xml CSXS/manifest.xml
```

### 3. 安装到Photoshop扩展目录

将整个插件文件夹复制到以下位置：

**Windows:**
```
C:\Program Files\Adobe\Adobe Photoshop 2025\Required\CEP\extensions\
```

**macOS:**
```
/Applications/Adobe Photoshop 2025/Required/CEP/extensions/
```

### 4. 启用调试模式（重要！）

CEP扩展需要启用调试模式才能运行。

**Windows方法：**
1. 按 `Win + R` 打开运行对话框
2. 输入 `regedit` 并回车
3. 导航到：`HKEY_CURRENT_USER\Software\Adobe\CSXS.9`
4. 如果不存在，创建该路径
5. 创建新的字符串值：`PlayerDebugMode`，值设为 `1`

**macOS方法：**
```bash
# 在终端中执行
defaults write com.adobe.CSXS.9 PlayerDebugMode 1
```

### 5. 重启Photoshop

完全关闭Photoshop，然后重新启动。

### 6. 查找插件

在Photoshop中：
1. 转到 `窗口` 菜单
2. 找到 `扩展功能` 子菜单
3. 应该能看到 `图层智能分离器` 选项

## 故障排除

### 问题1：插件不显示在菜单中
**解决方案：**
1. 检查文件结构是否正确
2. 确认 `CSXS/manifest.xml` 文件存在且格式正确
3. 确认已启用调试模式
4. 重启Photoshop

### 问题2：插件显示但无法加载
**解决方案：**
1. 检查 `jsx/host.jsx` 文件是否存在
2. 检查 `index.html` 文件路径是否正确
3. 查看Photoshop控制台错误信息

### 问题3：功能无法正常工作
**解决方案：**
1. 确保网络连接正常（OCR功能需要）
2. 检查浏览器控制台错误信息
3. 确认Photoshop版本兼容性

## 验证安装

安装成功后，您应该能够：
1. 在Photoshop扩展菜单中看到插件
2. 点击后打开插件面板
3. 看到完整的用户界面
4. 选择图层后可以使用各项功能

## 卸载方法

如需卸载插件：
1. 关闭Photoshop
2. 删除扩展目录中的插件文件夹
3. 重启Photoshop

## 注意事项

1. **调试模式**：生产环境中建议关闭调试模式
2. **权限**：确保对扩展目录有写入权限
3. **版本兼容性**：确认Photoshop版本支持CEP 9.0
4. **网络访问**：某些功能需要网络连接

## 开发者选项

如果您是开发者，可以使用以下方法进行调试：

### 启用远程调试
```bash
# Windows
set CEP_ENABLE_REMOTE_DEBUGGING=1

# macOS/Linux
export CEP_ENABLE_REMOTE_DEBUGGING=1
```

### 访问调试控制台
1. 启动Photoshop和插件
2. 在浏览器中访问：`http://localhost:8092`
3. 选择对应的插件进行调试

## 技术支持

如果遇到问题：
1. 检查本指南的故障排除部分
2. 查看Photoshop和浏览器控制台的错误信息
3. 确认所有文件完整且路径正确
4. 联系技术支持获取帮助
