/**
 * 文字提取器类 - 负责OCR文字识别和文字图层创建
 */
class TextExtractor {
    constructor() {
        this.tesseractWorker = null;
        this.isInitialized = false;
    }

    /**
     * 初始化Tesseract OCR引擎
     */
    async initializeOCR() {
        if (this.isInitialized) return;

        try {
            // 创建Tesseract Worker
            this.tesseractWorker = await Tesseract.createWorker();
            await this.tesseractWorker.loadLanguage('eng+chi_sim');
            await this.tesseractWorker.initialize('eng+chi_sim');
            this.isInitialized = true;
        } catch (error) {
            throw new Error('OCR引擎初始化失败: ' + error.message);
        }
    }

    /**
     * 从图层提取文字
     * @param {Layer} layer - Photoshop图层对象
     * @param {Object} options - 提取选项
     */
    async extractFromLayer(layer, options = {}) {
        const {
            language = 'chi_sim+eng',
            accuracy = 'balanced',
            onProgress = () => {}
        } = options;

        try {
            onProgress(10, '准备OCR引擎...');
            await this.initializeOCR();

            onProgress(20, '导出图层图像...');
            const imageData = await this.exportLayerAsImage(layer);

            onProgress(40, '执行文字识别...');
            const ocrResult = await this.performOCR(imageData, language, accuracy);

            onProgress(70, '创建文字图层...');
            const textLayers = await this.createTextLayers(ocrResult, layer);

            onProgress(100, '文字提取完成');

            return {
                success: true,
                extractedTexts: ocrResult.data.words.length,
                createdLayers: textLayers.length,
                textLayers: textLayers,
                confidence: this.calculateAverageConfidence(ocrResult)
            };

        } catch (error) {
            throw new Error('文字提取失败: ' + error.message);
        }
    }

    /**
     * 从图像数据提取文字（CEP版本）
     * @param {Uint8Array} imageData - 图像数据
     * @param {Object} options - 提取选项
     */
    async extractFromImageData(imageData, options = {}) {
        const {
            language = 'chi_sim+eng',
            accuracy = 'balanced',
            onProgress = () => {}
        } = options;

        try {
            onProgress(10, '准备OCR引擎...');
            await this.initializeOCR();

            onProgress(40, '执行文字识别...');
            const ocrResult = await this.performOCR(imageData, language, accuracy);

            onProgress(100, '文字识别完成');

            return ocrResult;

        } catch (error) {
            throw new Error('文字提取失败: ' + error.message);
        }
    }

    /**
     * 导出图层为图像数据
     */
    async exportLayerAsImage(layer) {
        try {
            // 获取图层边界
            const bounds = layer.bounds;

            // 创建临时文档用于导出
            const tempDoc = await app.documents.add({
                width: bounds.width,
                height: bounds.height,
                resolution: 72,
                mode: 'RGBColorMode'
            });

            // 复制图层到临时文档
            await layer.duplicate(tempDoc);

            // 导出为PNG格式
            const exportOptions = {
                format: 'png',
                quality: 100
            };

            const imageData = await tempDoc.exportAs(exportOptions);

            // 关闭临时文档
            await tempDoc.close();

            return imageData;

        } catch (error) {
            throw new Error('图层导出失败: ' + error.message);
        }
    }

    /**
     * 执行OCR识别
     */
    async performOCR(imageData, language, accuracy) {
        try {
            // 设置OCR参数
            const ocrOptions = {
                lang: language,
                tessedit_pageseg_mode: this.getPageSegMode(accuracy),
                tessedit_ocr_engine_mode: this.getOCREngineMode(accuracy)
            };

            // 执行OCR识别
            const result = await this.tesseractWorker.recognize(imageData, ocrOptions);

            return result;

        } catch (error) {
            throw new Error('OCR识别失败: ' + error.message);
        }
    }

    /**
     * 创建文字图层
     */
    async createTextLayers(ocrResult, originalLayer) {
        const textLayers = [];
        const words = ocrResult.data.words;

        try {
            for (const word of words) {
                if (word.confidence < 60) continue; // 跳过置信度低的文字

                // 创建文字图层
                const textLayer = await this.createSingleTextLayer(word, originalLayer);
                if (textLayer) {
                    textLayers.push(textLayer);
                }
            }

            return textLayers;

        } catch (error) {
            throw new Error('创建文字图层失败: ' + error.message);
        }
    }

    /**
     * 创建单个文字图层
     */
    async createSingleTextLayer(wordData, originalLayer) {
        try {
            const { text, bbox, confidence } = wordData;

            // 计算文字位置和大小
            const x = bbox.x0 + originalLayer.bounds.left;
            const y = bbox.y0 + originalLayer.bounds.top;
            const width = bbox.x1 - bbox.x0;
            const height = bbox.y1 - bbox.y0;

            // 估算字体大小
            const fontSize = Math.max(12, height * 0.8);

            // 分析文字颜色
            const textColor = await this.analyzeTextColor(originalLayer, bbox);

            // 创建文字图层
            const textLayer = await app.activeDocument.artLayers.add();
            textLayer.kind = 'text';
            textLayer.name = `提取文字: ${text}`;

            // 设置文字属性
            const textItem = textLayer.textItem;
            textItem.contents = text;
            textItem.position = [x, y];
            textItem.size = fontSize;
            textItem.color = textColor;
            textItem.font = this.selectAppropriateFont(text);

            // 设置图层位置
            textLayer.translate(0, 0);

            return {
                layer: textLayer,
                text: text,
                confidence: confidence,
                position: { x, y },
                size: fontSize
            };

        } catch (error) {
            console.error('创建单个文字图层失败:', error);
            return null;
        }
    }

    /**
     * 分析文字颜色
     */
    async analyzeTextColor(layer, bbox) {
        try {
            // 简化实现：返回黑色作为默认颜色
            // 实际实现中可以采样bbox区域的主要颜色
            const solidColor = new SolidColor();
            solidColor.rgb.red = 0;
            solidColor.rgb.green = 0;
            solidColor.rgb.blue = 0;
            return solidColor;

        } catch (error) {
            // 返回默认黑色
            const solidColor = new SolidColor();
            solidColor.rgb.red = 0;
            solidColor.rgb.green = 0;
            solidColor.rgb.blue = 0;
            return solidColor;
        }
    }

    /**
     * 选择合适的字体
     */
    selectAppropriateFont(text) {
        // 检测文字类型并选择合适字体
        const hasChinese = /[\u4e00-\u9fff]/.test(text);
        const hasJapanese = /[\u3040-\u309f\u30a0-\u30ff]/.test(text);
        const hasKorean = /[\uac00-\ud7af]/.test(text);

        if (hasChinese) {
            return 'SimSun'; // 宋体
        } else if (hasJapanese) {
            return 'MS Gothic';
        } else if (hasKorean) {
            return 'Malgun Gothic';
        } else {
            return 'Arial';
        }
    }

    /**
     * 获取页面分割模式
     */
    getPageSegMode(accuracy) {
        switch (accuracy) {
            case 'fast':
                return Tesseract.PSM.SINGLE_BLOCK;
            case 'balanced':
                return Tesseract.PSM.SINGLE_WORD;
            case 'accurate':
                return Tesseract.PSM.SINGLE_CHAR;
            default:
                return Tesseract.PSM.SINGLE_WORD;
        }
    }

    /**
     * 获取OCR引擎模式
     */
    getOCREngineMode(accuracy) {
        switch (accuracy) {
            case 'fast':
                return Tesseract.OEM.TESSERACT_ONLY;
            case 'balanced':
                return Tesseract.OEM.DEFAULT;
            case 'accurate':
                return Tesseract.OEM.LSTM_ONLY;
            default:
                return Tesseract.OEM.DEFAULT;
        }
    }

    /**
     * 计算平均置信度
     */
    calculateAverageConfidence(ocrResult) {
        const words = ocrResult.data.words;
        if (words.length === 0) return 0;

        const totalConfidence = words.reduce((sum, word) => sum + word.confidence, 0);
        return Math.round(totalConfidence / words.length);
    }

    /**
     * 清理资源
     */
    async cleanup() {
        if (this.tesseractWorker) {
            await this.tesseractWorker.terminate();
            this.tesseractWorker = null;
            this.isInitialized = false;
        }
    }
}
