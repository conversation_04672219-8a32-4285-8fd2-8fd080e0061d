/**
 * Photoshop CEP扩展 - JSX主机脚本
 * 提供与Photoshop应用程序的直接通信接口
 */

// 全局函数，供HTML/JS调用

/**
 * 获取当前活动文档信息
 */
function getActiveDocumentInfo() {
    try {
        if (app.documents.length === 0) {
            return JSON.stringify({
                success: false,
                error: "没有打开的文档"
            });
        }
        
        var doc = app.activeDocument;
        return JSON.stringify({
            success: true,
            data: {
                name: doc.name,
                width: doc.width.as('px'),
                height: doc.height.as('px'),
                resolution: doc.resolution,
                colorMode: doc.mode.toString(),
                layersCount: doc.layers.length
            }
        });
    } catch (error) {
        return JSON.stringify({
            success: false,
            error: error.toString()
        });
    }
}

/**
 * 获取当前活动图层信息
 */
function getActiveLayerInfo() {
    try {
        if (app.documents.length === 0) {
            return JSON.stringify({
                success: false,
                error: "没有打开的文档"
            });
        }
        
        var doc = app.activeDocument;
        var layer = doc.activeLayer;
        
        return JSON.stringify({
            success: true,
            data: {
                name: layer.name,
                kind: layer.kind.toString(),
                visible: layer.visible,
                opacity: layer.opacity,
                bounds: {
                    left: layer.bounds[0].as('px'),
                    top: layer.bounds[1].as('px'),
                    right: layer.bounds[2].as('px'),
                    bottom: layer.bounds[3].as('px')
                }
            }
        });
    } catch (error) {
        return JSON.stringify({
            success: false,
            error: error.toString()
        });
    }
}

/**
 * 获取选中的图层列表
 */
function getSelectedLayers() {
    try {
        if (app.documents.length === 0) {
            return JSON.stringify({
                success: false,
                error: "没有打开的文档"
            });
        }
        
        var doc = app.activeDocument;
        var layers = [];
        
        // 获取所有选中的图层
        var ref = new ActionReference();
        ref.putEnumerated(charIDToTypeID("Lyr "), charIDToTypeID("Ordn"), charIDToTypeID("Trgt"));
        
        try {
            var layerDesc = executeActionGet(ref);
            var layerIndex = layerDesc.getInteger(charIDToTypeID("ItmI"));
            var layer = doc.layers[layerIndex - 1];
            
            layers.push({
                name: layer.name,
                kind: layer.kind.toString(),
                visible: layer.visible,
                index: layerIndex
            });
        } catch (e) {
            // 如果没有选中多个图层，返回当前活动图层
            var layer = doc.activeLayer;
            layers.push({
                name: layer.name,
                kind: layer.kind.toString(),
                visible: layer.visible,
                index: 0
            });
        }
        
        return JSON.stringify({
            success: true,
            data: layers
        });
    } catch (error) {
        return JSON.stringify({
            success: false,
            error: error.toString()
        });
    }
}

/**
 * 导出图层为PNG文件
 */
function exportLayerAsPNG(layerName, exportPath) {
    try {
        var doc = app.activeDocument;
        var layer = findLayerByName(doc, layerName);
        
        if (!layer) {
            return JSON.stringify({
                success: false,
                error: "找不到指定图层: " + layerName
            });
        }
        
        // 隐藏其他图层，只显示目标图层
        var originalVisibility = [];
        for (var i = 0; i < doc.layers.length; i++) {
            originalVisibility.push(doc.layers[i].visible);
            doc.layers[i].visible = false;
        }
        layer.visible = true;
        
        // 导出PNG
        var exportFile = new File(exportPath);
        var pngOptions = new PNGSaveOptions();
        pngOptions.compression = 6;
        pngOptions.interlaced = false;
        
        doc.exportDocument(exportFile, ExportType.SAVEFORWEB, pngOptions);
        
        // 恢复图层可见性
        for (var i = 0; i < doc.layers.length; i++) {
            doc.layers[i].visible = originalVisibility[i];
        }
        
        return JSON.stringify({
            success: true,
            data: {
                exportPath: exportPath,
                layerName: layerName
            }
        });
    } catch (error) {
        return JSON.stringify({
            success: false,
            error: error.toString()
        });
    }
}

/**
 * 创建文字图层
 */
function createTextLayer(text, x, y, fontSize, fontFamily, color, layerName) {
    try {
        var doc = app.activeDocument;
        
        // 创建文字图层
        var textLayer = doc.artLayers.add();
        textLayer.kind = LayerKind.TEXT;
        textLayer.name = layerName || ("文字图层_" + text.substring(0, 10));
        
        // 设置文字属性
        var textItem = textLayer.textItem;
        textItem.contents = text;
        textItem.position = [UnitValue(x, "px"), UnitValue(y, "px")];
        textItem.size = UnitValue(fontSize, "px");
        textItem.font = fontFamily || "Arial";
        
        // 设置颜色
        if (color) {
            var textColor = new SolidColor();
            textColor.rgb.red = color.r || 0;
            textColor.rgb.green = color.g || 0;
            textColor.rgb.blue = color.b || 0;
            textItem.color = textColor;
        }
        
        return JSON.stringify({
            success: true,
            data: {
                layerName: textLayer.name,
                text: text,
                position: { x: x, y: y },
                fontSize: fontSize
            }
        });
    } catch (error) {
        return JSON.stringify({
            success: false,
            error: error.toString()
        });
    }
}

/**
 * 复制图层
 */
function duplicateLayer(layerName, newLayerName) {
    try {
        var doc = app.activeDocument;
        var layer = findLayerByName(doc, layerName);
        
        if (!layer) {
            return JSON.stringify({
                success: false,
                error: "找不到指定图层: " + layerName
            });
        }
        
        var duplicatedLayer = layer.duplicate();
        duplicatedLayer.name = newLayerName || (layerName + "_副本");
        
        return JSON.stringify({
            success: true,
            data: {
                originalLayer: layerName,
                newLayer: duplicatedLayer.name
            }
        });
    } catch (error) {
        return JSON.stringify({
            success: false,
            error: error.toString()
        });
    }
}

/**
 * 应用蒙版到图层
 */
function applyMaskToLayer(layerName, maskImagePath) {
    try {
        var doc = app.activeDocument;
        var layer = findLayerByName(doc, layerName);
        
        if (!layer) {
            return JSON.stringify({
                success: false,
                error: "找不到指定图层: " + layerName
            });
        }
        
        // 选择图层
        doc.activeLayer = layer;
        
        // 添加图层蒙版
        doc.selection.selectAll();
        doc.selection.clear();
        
        // 这里需要加载蒙版图像并应用
        // 简化实现：创建一个基本的蒙版
        var maskChannel = doc.channels.add();
        maskChannel.name = "临时蒙版";
        
        return JSON.stringify({
            success: true,
            data: {
                layerName: layerName,
                maskApplied: true
            }
        });
    } catch (error) {
        return JSON.stringify({
            success: false,
            error: error.toString()
        });
    }
}

/**
 * 删除图层
 */
function deleteLayer(layerName) {
    try {
        var doc = app.activeDocument;
        var layer = findLayerByName(doc, layerName);
        
        if (!layer) {
            return JSON.stringify({
                success: false,
                error: "找不到指定图层: " + layerName
            });
        }
        
        layer.remove();
        
        return JSON.stringify({
            success: true,
            data: {
                deletedLayer: layerName
            }
        });
    } catch (error) {
        return JSON.stringify({
            success: false,
            error: error.toString()
        });
    }
}

/**
 * 辅助函数：根据名称查找图层
 */
function findLayerByName(doc, layerName) {
    for (var i = 0; i < doc.layers.length; i++) {
        if (doc.layers[i].name === layerName) {
            return doc.layers[i];
        }
        // 如果是图层组，递归查找
        if (doc.layers[i].typename === "LayerSet") {
            var found = findLayerInGroup(doc.layers[i], layerName);
            if (found) return found;
        }
    }
    return null;
}

/**
 * 在图层组中查找图层
 */
function findLayerInGroup(layerSet, layerName) {
    for (var i = 0; i < layerSet.layers.length; i++) {
        if (layerSet.layers[i].name === layerName) {
            return layerSet.layers[i];
        }
        if (layerSet.layers[i].typename === "LayerSet") {
            var found = findLayerInGroup(layerSet.layers[i], layerName);
            if (found) return found;
        }
    }
    return null;
}

/**
 * 获取系统临时目录
 */
function getTempDirectory() {
    try {
        var tempFolder = Folder.temp;
        var pluginTempFolder = new Folder(tempFolder + "/ps_layer_separator");
        if (!pluginTempFolder.exists) {
            pluginTempFolder.create();
        }
        
        return JSON.stringify({
            success: true,
            data: {
                path: pluginTempFolder.fsName
            }
        });
    } catch (error) {
        return JSON.stringify({
            success: false,
            error: error.toString()
        });
    }
}

/**
 * 显示进度条（Photoshop内置）
 */
function showProgress(message, progress) {
    try {
        if (progress >= 0 && progress <= 100) {
            app.displayDialogs = DialogModes.NO;
            // 这里可以使用Photoshop的进度显示功能
            // 简化实现
        }
        
        return JSON.stringify({
            success: true,
            data: {
                message: message,
                progress: progress
            }
        });
    } catch (error) {
        return JSON.stringify({
            success: false,
            error: error.toString()
        });
    }
}
