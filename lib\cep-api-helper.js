/**
 * CEP API助手类 - 封装CEP扩展与Photoshop的通信
 */
class CEPAPIHelper {
    constructor() {
        this.csInterface = new CSInterface();
        this.tempDir = null;
        this.initializeTempDirectory();
    }

    /**
     * 初始化临时目录
     */
    async initializeTempDirectory() {
        try {
            const result = await this.evalScript('getTempDirectory()');
            const data = JSON.parse(result);
            if (data.success) {
                this.tempDir = data.data.path;
            }
        } catch (error) {
            console.warn('初始化临时目录失败:', error);
        }
    }

    /**
     * 执行JSX脚本
     */
    evalScript(script) {
        return new Promise((resolve, reject) => {
            this.csInterface.evalScript(script, (result) => {
                if (result === 'EvalScript error.') {
                    reject(new Error('JSX脚本执行失败'));
                } else {
                    resolve(result);
                }
            });
        });
    }

    /**
     * 获取当前活动文档
     */
    async getActiveDocument() {
        try {
            const result = await this.evalScript('getActiveDocumentInfo()');
            const data = JSON.parse(result);
            
            if (!data.success) {
                throw new Error(data.error);
            }
            
            return data.data;
        } catch (error) {
            throw new Error('获取活动文档失败: ' + error.message);
        }
    }

    /**
     * 获取当前活动图层
     */
    async getActiveLayer() {
        try {
            const result = await this.evalScript('getActiveLayerInfo()');
            const data = JSON.parse(result);
            
            if (!data.success) {
                throw new Error(data.error);
            }
            
            return data.data;
        } catch (error) {
            throw new Error('获取活动图层失败: ' + error.message);
        }
    }

    /**
     * 获取选中的图层列表
     */
    async getSelectedLayers() {
        try {
            const result = await this.evalScript('getSelectedLayers()');
            const data = JSON.parse(result);
            
            if (!data.success) {
                throw new Error(data.error);
            }
            
            return data.data;
        } catch (error) {
            console.warn('获取选中图层失败，使用活动图层:', error);
            const activeLayer = await this.getActiveLayer();
            return activeLayer ? [activeLayer] : [];
        }
    }

    /**
     * 导出图层为图像
     */
    async exportLayerAsImage(layerName) {
        try {
            if (!this.tempDir) {
                throw new Error('临时目录未初始化');
            }

            const timestamp = Date.now();
            const fileName = `layer_${timestamp}.png`;
            const exportPath = this.tempDir + '/' + fileName;
            
            const script = `exportLayerAsPNG("${layerName}", "${exportPath}")`;
            const result = await this.evalScript(script);
            const data = JSON.parse(result);
            
            if (!data.success) {
                throw new Error(data.error);
            }

            // 读取导出的文件
            const imageData = await this.readImageFile(exportPath);
            return imageData;

        } catch (error) {
            throw new Error('导出图层失败: ' + error.message);
        }
    }

    /**
     * 读取图像文件
     */
    async readImageFile(filePath) {
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            xhr.open('GET', 'file://' + filePath, true);
            xhr.responseType = 'arraybuffer';
            
            xhr.onload = function() {
                if (xhr.status === 200) {
                    resolve(new Uint8Array(xhr.response));
                } else {
                    reject(new Error('读取文件失败'));
                }
            };
            
            xhr.onerror = function() {
                reject(new Error('文件读取错误'));
            };
            
            xhr.send();
        });
    }

    /**
     * 创建文字图层
     */
    async createTextLayer(text, options = {}) {
        try {
            const {
                x = 0,
                y = 0,
                fontSize = 12,
                fontFamily = 'Arial',
                color = { r: 0, g: 0, b: 0 },
                name = '文字图层'
            } = options;

            const script = `createTextLayer("${text}", ${x}, ${y}, ${fontSize}, "${fontFamily}", ${JSON.stringify(color)}, "${name}")`;
            const result = await this.evalScript(script);
            const data = JSON.parse(result);
            
            if (!data.success) {
                throw new Error(data.error);
            }

            return data.data;

        } catch (error) {
            throw new Error('创建文字图层失败: ' + error.message);
        }
    }

    /**
     * 复制图层
     */
    async duplicateLayer(layerName, newLayerName = null) {
        try {
            const script = `duplicateLayer("${layerName}", "${newLayerName || layerName + '_副本'}")`;
            const result = await this.evalScript(script);
            const data = JSON.parse(result);
            
            if (!data.success) {
                throw new Error(data.error);
            }

            return data.data;

        } catch (error) {
            throw new Error('复制图层失败: ' + error.message);
        }
    }

    /**
     * 应用蒙版到图层
     */
    async applyMaskToLayer(layerName, maskData) {
        try {
            // 将蒙版数据保存为临时文件
            const maskPath = await this.saveMaskToTempFile(maskData);
            
            const script = `applyMaskToLayer("${layerName}", "${maskPath}")`;
            const result = await this.evalScript(script);
            const data = JSON.parse(result);
            
            if (!data.success) {
                throw new Error(data.error);
            }

            return data.data;

        } catch (error) {
            throw new Error('应用蒙版失败: ' + error.message);
        }
    }

    /**
     * 保存蒙版数据到临时文件
     */
    async saveMaskToTempFile(maskData) {
        try {
            if (!this.tempDir) {
                throw new Error('临时目录未初始化');
            }

            const timestamp = Date.now();
            const fileName = `mask_${timestamp}.png`;
            const filePath = this.tempDir + '/' + fileName;

            // 将Uint8Array转换为Blob
            const blob = new Blob([maskData], { type: 'image/png' });
            
            // 使用FileReader读取为base64
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = function() {
                    // 保存base64数据到文件
                    // 这里需要使用CEP的文件系统API
                    resolve(filePath);
                };
                reader.onerror = reject;
                reader.readAsDataURL(blob);
            });

        } catch (error) {
            throw new Error('保存蒙版文件失败: ' + error.message);
        }
    }

    /**
     * 删除图层
     */
    async deleteLayer(layerName) {
        try {
            const script = `deleteLayer("${layerName}")`;
            const result = await this.evalScript(script);
            const data = JSON.parse(result);
            
            if (!data.success) {
                throw new Error(data.error);
            }

            return data.data;

        } catch (error) {
            throw new Error('删除图层失败: ' + error.message);
        }
    }

    /**
     * 显示进度
     */
    async showProgress(message, progress) {
        try {
            const script = `showProgress("${message}", ${progress})`;
            await this.evalScript(script);
        } catch (error) {
            console.warn('显示进度失败:', error);
        }
    }

    /**
     * 获取图层边界
     */
    getLayerBounds(layer) {
        if (layer.bounds) {
            return {
                left: layer.bounds.left,
                top: layer.bounds.top,
                right: layer.bounds.right,
                bottom: layer.bounds.bottom,
                width: layer.bounds.right - layer.bounds.left,
                height: layer.bounds.bottom - layer.bounds.top
            };
        }
        return null;
    }

    /**
     * 检查图层类型
     */
    isTextLayer(layer) {
        return layer.kind === 'LayerKind.TEXT' || layer.kind === 'TEXT';
    }

    /**
     * 检查图层是否可见
     */
    isLayerVisible(layer) {
        return layer.visible;
    }

    /**
     * 获取扩展信息
     */
    getExtensionInfo() {
        return {
            id: this.csInterface.getExtensionID(),
            version: this.csInterface.getCurrentApiVersion(),
            hostEnvironment: this.csInterface.getHostEnvironment()
        };
    }

    /**
     * 打开URL
     */
    openURL(url) {
        this.csInterface.openURLInDefaultBrowser(url);
    }

    /**
     * 获取系统路径
     */
    getSystemPath(pathType) {
        return this.csInterface.getSystemPath(pathType);
    }

    /**
     * 注册事件监听器
     */
    addEventListener(type, listener, obj) {
        this.csInterface.addEventListener(type, listener, obj);
    }

    /**
     * 移除事件监听器
     */
    removeEventListener(type, listener, obj) {
        this.csInterface.removeEventListener(type, listener, obj);
    }

    /**
     * 请求打开扩展
     */
    requestOpenExtension(extensionId, params) {
        this.csInterface.requestOpenExtension(extensionId, params);
    }

    /**
     * 获取扩展根目录
     */
    getExtensionRoot() {
        return this.csInterface.getSystemPath(SystemPath.EXTENSION);
    }

    /**
     * 清理临时文件
     */
    async cleanupTempFiles() {
        try {
            if (this.tempDir) {
                const script = `
                    var tempFolder = new Folder("${this.tempDir}");
                    if (tempFolder.exists) {
                        var files = tempFolder.getFiles();
                        for (var i = 0; i < files.length; i++) {
                            if (files[i] instanceof File) {
                                files[i].remove();
                            }
                        }
                    }
                    "cleanup_complete"
                `;
                await this.evalScript(script);
            }
        } catch (error) {
            console.warn('清理临时文件失败:', error);
        }
    }
}
