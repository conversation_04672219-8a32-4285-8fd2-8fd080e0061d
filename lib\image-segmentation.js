/**
 * 图像分割类 - 负责元素背景分离和透明化处理
 */
class ImageSegmentation {
    constructor() {
        this.apiKey = null; // Remove.bg API密钥
        this.segmentationModel = null;
    }

    /**
     * 分离元素和背景
     * @param {Layer} layer - Photoshop图层对象
     * @param {Object} options - 分离选项
     */
    async separateElement(layer, options = {}) {
        const {
            mode = 'auto',
            quality = 'standard',
            onProgress = () => {}
        } = options;

        try {
            onProgress(10, '准备图像分割...');
            
            // 导出图层图像
            onProgress(20, '导出图层图像...');
            const imageData = await this.exportLayerAsImage(layer);

            // 执行背景分离
            onProgress(40, '执行背景分离...');
            const maskData = await this.performSegmentation(imageData, mode, quality);

            // 创建透明图层
            onProgress(70, '创建透明图层...');
            const resultLayer = await this.createTransparentLayer(layer, maskData);

            onProgress(100, '元素分离完成');

            return {
                success: true,
                originalLayer: layer.name,
                newLayer: resultLayer.name,
                mode: mode,
                quality: quality
            };

        } catch (error) {
            throw new Error('元素分离失败: ' + error.message);
        }
    }

    /**
     * 导出图层为图像数据
     */
    async exportLayerAsImage(layer) {
        try {
            // 获取图层边界
            const bounds = layer.bounds;
            
            // 创建临时文档
            const tempDoc = await app.documents.add({
                width: bounds.width,
                height: bounds.height,
                resolution: layer.parent.resolution,
                mode: 'RGBColorMode'
            });

            // 复制图层
            const duplicatedLayer = await layer.duplicate(tempDoc);
            
            // 导出为PNG
            const exportOptions = {
                format: 'png',
                quality: 100,
                transparency: true
            };

            const imageBuffer = await tempDoc.exportAs(exportOptions);
            
            // 关闭临时文档
            await tempDoc.close();

            return imageBuffer;

        } catch (error) {
            throw new Error('图层导出失败: ' + error.message);
        }
    }

    /**
     * 执行图像分割
     */
    async performSegmentation(imageData, mode, quality) {
        try {
            switch (mode) {
                case 'auto':
                    return await this.autoSegmentation(imageData, quality);
                case 'person':
                    return await this.personSegmentation(imageData, quality);
                case 'object':
                    return await this.objectSegmentation(imageData, quality);
                default:
                    return await this.autoSegmentation(imageData, quality);
            }
        } catch (error) {
            throw new Error('图像分割失败: ' + error.message);
        }
    }

    /**
     * 自动分割
     */
    async autoSegmentation(imageData, quality) {
        try {
            // 使用Remove.bg API或本地AI模型
            if (this.apiKey) {
                return await this.removeBgAPI(imageData, quality);
            } else {
                return await this.localSegmentation(imageData, quality);
            }
        } catch (error) {
            throw new Error('自动分割失败: ' + error.message);
        }
    }

    /**
     * 人物分割
     */
    async personSegmentation(imageData, quality) {
        try {
            // 专门针对人物的分割算法
            return await this.removeBgAPI(imageData, quality, 'person');
        } catch (error) {
            throw new Error('人物分割失败: ' + error.message);
        }
    }

    /**
     * 物体分割
     */
    async objectSegmentation(imageData, quality) {
        try {
            // 针对物体的分割算法
            return await this.removeBgAPI(imageData, quality, 'object');
        } catch (error) {
            throw new Error('物体分割失败: ' + error.message);
        }
    }

    /**
     * 使用Remove.bg API进行分割
     */
    async removeBgAPI(imageData, quality, type = 'auto') {
        try {
            const formData = new FormData();
            formData.append('image_file', new Blob([imageData], { type: 'image/png' }));
            formData.append('size', this.getAPIQuality(quality));
            
            if (type !== 'auto') {
                formData.append('type', type);
            }

            const response = await fetch('https://api.remove.bg/v1.0/removebg', {
                method: 'POST',
                headers: {
                    'X-Api-Key': this.apiKey
                },
                body: formData
            });

            if (!response.ok) {
                throw new Error(`API请求失败: ${response.status}`);
            }

            const resultBuffer = await response.arrayBuffer();
            return new Uint8Array(resultBuffer);

        } catch (error) {
            throw new Error('Remove.bg API调用失败: ' + error.message);
        }
    }

    /**
     * 本地分割算法（简化实现）
     */
    async localSegmentation(imageData, quality) {
        try {
            // 这里实现本地的图像分割算法
            // 可以使用Canvas API进行简单的颜色分割
            return await this.canvasBasedSegmentation(imageData, quality);
        } catch (error) {
            throw new Error('本地分割失败: ' + error.message);
        }
    }

    /**
     * 基于Canvas的简单分割
     */
    async canvasBasedSegmentation(imageData, quality) {
        try {
            // 创建Canvas元素
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            // 创建图像对象
            const img = new Image();
            img.src = URL.createObjectURL(new Blob([imageData], { type: 'image/png' }));
            
            await new Promise((resolve) => {
                img.onload = resolve;
            });

            canvas.width = img.width;
            canvas.height = img.height;
            
            // 绘制图像
            ctx.drawImage(img, 0, 0);
            
            // 获取图像数据
            const imgData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imgData.data;

            // 简单的边缘检测和背景移除
            this.simpleBackgroundRemoval(data, canvas.width, canvas.height);

            // 更新Canvas
            ctx.putImageData(imgData, 0, 0);

            // 导出结果
            return new Promise((resolve) => {
                canvas.toBlob((blob) => {
                    const reader = new FileReader();
                    reader.onload = () => {
                        resolve(new Uint8Array(reader.result));
                    };
                    reader.readAsArrayBuffer(blob);
                }, 'image/png');
            });

        } catch (error) {
            throw new Error('Canvas分割失败: ' + error.message);
        }
    }

    /**
     * 简单背景移除算法
     */
    simpleBackgroundRemoval(data, width, height) {
        // 简化的背景移除算法
        // 检测边缘像素作为背景色参考
        const backgroundColors = this.detectBackgroundColors(data, width, height);
        
        for (let i = 0; i < data.length; i += 4) {
            const r = data[i];
            const g = data[i + 1];
            const b = data[i + 2];
            
            // 检查是否为背景色
            if (this.isBackgroundColor(r, g, b, backgroundColors)) {
                data[i + 3] = 0; // 设置为透明
            }
        }
    }

    /**
     * 检测背景颜色
     */
    detectBackgroundColors(data, width, height) {
        const colors = new Map();
        
        // 采样边缘像素
        const samplePoints = [
            // 四个角落
            [0, 0], [width - 1, 0], [0, height - 1], [width - 1, height - 1],
            // 边缘中点
            [Math.floor(width / 2), 0], [Math.floor(width / 2), height - 1],
            [0, Math.floor(height / 2)], [width - 1, Math.floor(height / 2)]
        ];

        for (const [x, y] of samplePoints) {
            const index = (y * width + x) * 4;
            const color = `${data[index]},${data[index + 1]},${data[index + 2]}`;
            colors.set(color, (colors.get(color) || 0) + 1);
        }

        // 返回最常见的颜色作为背景色
        return Array.from(colors.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, 3)
            .map(([color]) => color.split(',').map(Number));
    }

    /**
     * 检查是否为背景色
     */
    isBackgroundColor(r, g, b, backgroundColors) {
        const threshold = 30; // 颜色相似度阈值
        
        return backgroundColors.some(([br, bg, bb]) => {
            const distance = Math.sqrt(
                Math.pow(r - br, 2) + 
                Math.pow(g - bg, 2) + 
                Math.pow(b - bb, 2)
            );
            return distance < threshold;
        });
    }

    /**
     * 创建透明图层
     */
    async createTransparentLayer(originalLayer, maskData) {
        try {
            // 创建新图层
            const newLayer = await originalLayer.duplicate();
            newLayer.name = originalLayer.name + '_透明背景';

            // 应用蒙版
            await this.applyMask(newLayer, maskData);

            return newLayer;

        } catch (error) {
            throw new Error('创建透明图层失败: ' + error.message);
        }
    }

    /**
     * 应用蒙版
     */
    async applyMask(layer, maskData) {
        try {
            // 创建临时文档用于处理蒙版
            const tempDoc = await app.documents.add({
                width: layer.bounds.width,
                height: layer.bounds.height,
                resolution: 72,
                mode: 'RGBColorMode'
            });

            // 导入蒙版数据
            // 这里需要将maskData转换为Photoshop可以理解的格式
            // 实际实现中可能需要使用更复杂的方法

            await tempDoc.close();

        } catch (error) {
            throw new Error('应用蒙版失败: ' + error.message);
        }
    }

    /**
     * 获取API质量参数
     */
    getAPIQuality(quality) {
        switch (quality) {
            case 'draft':
                return 'preview';
            case 'standard':
                return 'regular';
            case 'high':
                return 'full';
            default:
                return 'regular';
        }
    }

    /**
     * 设置API密钥
     */
    setAPIKey(apiKey) {
        this.apiKey = apiKey;
    }

    /**
     * 检查API可用性
     */
    async checkAPIAvailability() {
        if (!this.apiKey) {
            return false;
        }

        try {
            const response = await fetch('https://api.remove.bg/v1.0/account', {
                headers: {
                    'X-Api-Key': this.apiKey
                }
            });
            return response.ok;
        } catch (error) {
            return false;
        }
    }
}
