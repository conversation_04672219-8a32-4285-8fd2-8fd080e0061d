@echo off
chcp 65001 >nul
echo ========================================
echo PS图层智能分离器 - 自动安装脚本
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ 检测到管理员权限
) else (
    echo ❌ 需要管理员权限才能安装到Photoshop目录
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

:: 设置变量
set "PLUGIN_NAME=ps图片转"
set "PS_EXTENSIONS_DIR=C:\Program Files\Adobe\Adobe Photoshop 2025\Required\CEP\extensions"
set "CURRENT_DIR=%~dp0"

echo 当前插件目录: %CURRENT_DIR%
echo Photoshop扩展目录: %PS_EXTENSIONS_DIR%
echo.

:: 检查Photoshop扩展目录是否存在
if not exist "%PS_EXTENSIONS_DIR%" (
    echo ❌ 未找到Photoshop扩展目录
    echo 请确认Photoshop 2025已正确安装
    echo 或手动修改脚本中的路径
    pause
    exit /b 1
)

:: 检查目标目录是否已存在
if exist "%PS_EXTENSIONS_DIR%\%PLUGIN_NAME%" (
    echo ⚠️  检测到已存在的插件安装
    set /p "OVERWRITE=是否覆盖安装? (y/n): "
    if /i not "%OVERWRITE%"=="y" (
        echo 安装已取消
        pause
        exit /b 0
    )
    echo 正在删除旧版本...
    rmdir /s /q "%PS_EXTENSIONS_DIR%\%PLUGIN_NAME%"
)

:: 复制插件文件
echo 正在安装插件...
xcopy "%CURRENT_DIR%*" "%PS_EXTENSIONS_DIR%\%PLUGIN_NAME%\" /E /I /H /Y >nul

if %errorLevel% == 0 (
    echo ✓ 插件文件复制完成
) else (
    echo ❌ 插件文件复制失败
    pause
    exit /b 1
)

:: 启用CEP调试模式
echo 正在启用CEP调试模式...
reg add "HKEY_CURRENT_USER\Software\Adobe\CSXS.9" /v PlayerDebugMode /t REG_SZ /d 1 /f >nul 2>&1

if %errorLevel% == 0 (
    echo ✓ CEP调试模式已启用
) else (
    echo ⚠️  CEP调试模式启用失败，请手动设置
)

echo.
echo ========================================
echo 🎉 安装完成！
echo ========================================
echo.
echo 接下来的步骤：
echo 1. 完全关闭Photoshop（如果正在运行）
echo 2. 重新启动Photoshop
echo 3. 在菜单中找到：窗口 ^> 扩展功能 ^> 图层智能分离器
echo.
echo 如果插件没有出现，请检查：
echo - Photoshop版本是否为2021或更高版本
echo - 是否已完全重启Photoshop
echo - 查看CEP_INSTALL_GUIDE.md获取详细故障排除指南
echo.

pause
