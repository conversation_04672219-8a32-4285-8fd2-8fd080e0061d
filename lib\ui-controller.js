/**
 * UI控制器类 - 负责用户界面交互和状态管理
 */
class UIController {
    constructor() {
        this.progressContainer = document.getElementById('progressContainer');
        this.progressFill = document.getElementById('progressFill');
        this.progressText = document.getElementById('progressText');
        this.progressTitle = document.getElementById('progressTitle');
        this.resultContainer = document.getElementById('resultContainer');
        this.resultContent = document.getElementById('resultContent');
        
        this.currentProgress = 0;
        this.isProgressVisible = false;
    }

    /**
     * 显示进度条
     * @param {string} title - 进度标题
     * @param {number} progress - 进度百分比 (0-100)
     * @param {string} text - 进度描述文本
     */
    showProgress(title, progress = 0, text = '') {
        this.progressTitle.textContent = title;
        this.updateProgress(progress, text);
        this.progressContainer.classList.remove('hidden');
        this.isProgressVisible = true;
    }

    /**
     * 更新进度
     * @param {number} progress - 进度百分比 (0-100)
     * @param {string} text - 进度描述文本
     */
    updateProgress(progress, text = '') {
        this.currentProgress = Math.max(0, Math.min(100, progress));
        this.progressFill.style.width = `${this.currentProgress}%`;
        
        if (text) {
            this.progressText.textContent = text;
        }

        // 添加进度动画效果
        this.progressFill.style.transition = 'width 0.3s ease';
    }

    /**
     * 隐藏进度条
     */
    hideProgress() {
        this.progressContainer.classList.add('hidden');
        this.isProgressVisible = false;
        this.currentProgress = 0;
        this.progressFill.style.width = '0%';
    }

    /**
     * 显示结果
     * @param {string} title - 结果标题
     * @param {Object} result - 结果数据
     */
    showResult(title, result) {
        this.hideProgress();
        
        const resultHTML = this.formatResult(title, result);
        this.resultContent.innerHTML = resultHTML;
        this.resultContainer.classList.remove('hidden');

        // 添加显示动画
        this.resultContainer.style.animation = 'fadeIn 0.3s ease-out';
    }

    /**
     * 隐藏结果
     */
    hideResult() {
        this.resultContainer.classList.add('hidden');
    }

    /**
     * 格式化结果显示
     * @param {string} title - 结果标题
     * @param {Object} result - 结果数据
     */
    formatResult(title, result) {
        let html = `<h4>${title}</h4>`;

        if (result.success) {
            html += '<div class="result-success">';
            
            if (result.extractedTexts !== undefined) {
                // 文字提取结果
                html += `
                    <p><strong>✅ 提取成功</strong></p>
                    <ul>
                        <li>识别文字数量: ${result.extractedTexts}</li>
                        <li>创建图层数量: ${result.createdLayers}</li>
                        <li>平均置信度: ${result.confidence}%</li>
                    </ul>
                `;

                if (result.textLayers && result.textLayers.length > 0) {
                    html += '<h5>创建的文字图层:</h5><ul>';
                    result.textLayers.forEach((textLayer, index) => {
                        html += `
                            <li>
                                <strong>${textLayer.text}</strong> 
                                (置信度: ${textLayer.confidence}%, 
                                位置: ${Math.round(textLayer.position.x)}, ${Math.round(textLayer.position.y)})
                            </li>
                        `;
                    });
                    html += '</ul>';
                }
            }

            if (result.originalLayer !== undefined) {
                // 元素分离结果
                html += `
                    <p><strong>✅ 分离成功</strong></p>
                    <ul>
                        <li>原始图层: ${result.originalLayer}</li>
                        <li>新图层: ${result.newLayer}</li>
                        <li>分离模式: ${this.getModeDisplayName(result.mode)}</li>
                        <li>处理质量: ${this.getQualityDisplayName(result.quality)}</li>
                    </ul>
                `;
            }

            if (result.totalProcessed !== undefined) {
                // 批量处理结果
                html += `
                    <p><strong>✅ 批量处理完成</strong></p>
                    <ul>
                        <li>处理图层总数: ${result.totalProcessed}</li>
                        <li>操作总数: ${result.results.length}</li>
                    </ul>
                `;

                if (result.results && result.results.length > 0) {
                    html += '<h5>处理详情:</h5><ul>';
                    result.results.forEach((item, index) => {
                        const typeDisplay = item.type === 'text' ? '文字提取' : '元素分离';
                        html += `
                            <li>
                                <strong>${item.layer}</strong> - ${typeDisplay}
                                ${item.result.success ? '✅' : '❌'}
                            </li>
                        `;
                    });
                    html += '</ul>';
                }
            }

            html += '</div>';
        } else {
            html += `
                <div class="result-error">
                    <p><strong>❌ 处理失败</strong></p>
                    <p>${result.error || '未知错误'}</p>
                </div>
            `;
        }

        return html;
    }

    /**
     * 显示错误消息
     * @param {string} message - 错误消息
     */
    showError(message) {
        this.hideProgress();
        this.showMessage(message, 'error');
    }

    /**
     * 显示消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 (success, warning, error, info)
     */
    showMessage(message, type = 'info') {
        // 创建消息元素
        const messageEl = document.createElement('div');
        messageEl.className = `message message-${type}`;
        messageEl.innerHTML = `
            <span class="message-icon">${this.getMessageIcon(type)}</span>
            <span class="message-text">${message}</span>
            <button class="message-close" onclick="this.parentElement.remove()">×</button>
        `;

        // 添加样式
        messageEl.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${this.getMessageColor(type)};
            color: white;
            padding: 12px 16px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            display: flex;
            align-items: center;
            gap: 8px;
            max-width: 300px;
            z-index: 1000;
            animation: slideIn 0.3s ease-out;
        `;

        // 添加到页面
        document.body.appendChild(messageEl);

        // 自动移除
        setTimeout(() => {
            if (messageEl.parentElement) {
                messageEl.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => {
                    messageEl.remove();
                }, 300);
            }
        }, 5000);
    }

    /**
     * 获取消息图标
     */
    getMessageIcon(type) {
        const icons = {
            success: '✅',
            warning: '⚠️',
            error: '❌',
            info: 'ℹ️'
        };
        return icons[type] || icons.info;
    }

    /**
     * 获取消息颜色
     */
    getMessageColor(type) {
        const colors = {
            success: '#00aa00',
            warning: '#ff9900',
            error: '#cc0000',
            info: '#0066cc'
        };
        return colors[type] || colors.info;
    }

    /**
     * 获取模式显示名称
     */
    getModeDisplayName(mode) {
        const modes = {
            auto: '自动分离',
            person: '人物分离',
            object: '物体分离'
        };
        return modes[mode] || mode;
    }

    /**
     * 获取质量显示名称
     */
    getQualityDisplayName(quality) {
        const qualities = {
            draft: '草稿质量',
            standard: '标准质量',
            high: '高质量'
        };
        return qualities[quality] || quality;
    }

    /**
     * 禁用所有按钮
     */
    disableAllButtons() {
        const buttons = document.querySelectorAll('button:not(#cancelBtn):not(#closeResultBtn)');
        buttons.forEach(button => {
            button.disabled = true;
            button.classList.add('disabled');
        });
    }

    /**
     * 启用所有按钮
     */
    enableAllButtons() {
        const buttons = document.querySelectorAll('button');
        buttons.forEach(button => {
            button.disabled = false;
            button.classList.remove('disabled');
        });
    }

    /**
     * 设置按钮加载状态
     * @param {string} buttonId - 按钮ID
     * @param {boolean} loading - 是否加载中
     */
    setButtonLoading(buttonId, loading) {
        const button = document.getElementById(buttonId);
        if (!button) return;

        if (loading) {
            button.disabled = true;
            button.classList.add('loading');
            const originalText = button.innerHTML;
            button.dataset.originalText = originalText;
            button.innerHTML = '<span class="loading-spinner">⏳</span> 处理中...';
        } else {
            button.disabled = false;
            button.classList.remove('loading');
            if (button.dataset.originalText) {
                button.innerHTML = button.dataset.originalText;
                delete button.dataset.originalText;
            }
        }
    }

    /**
     * 显示确认对话框
     * @param {string} message - 确认消息
     * @param {Function} onConfirm - 确认回调
     * @param {Function} onCancel - 取消回调
     */
    showConfirmDialog(message, onConfirm, onCancel) {
        const dialog = document.createElement('div');
        dialog.className = 'confirm-dialog';
        dialog.innerHTML = `
            <div class="dialog-overlay">
                <div class="dialog-content">
                    <h3>确认操作</h3>
                    <p>${message}</p>
                    <div class="dialog-buttons">
                        <button class="confirm-btn">确认</button>
                        <button class="cancel-btn">取消</button>
                    </div>
                </div>
            </div>
        `;

        // 添加样式
        dialog.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 2000;
        `;

        // 事件处理
        dialog.querySelector('.confirm-btn').onclick = () => {
            dialog.remove();
            if (onConfirm) onConfirm();
        };

        dialog.querySelector('.cancel-btn').onclick = () => {
            dialog.remove();
            if (onCancel) onCancel();
        };

        dialog.querySelector('.dialog-overlay').onclick = (e) => {
            if (e.target === e.currentTarget) {
                dialog.remove();
                if (onCancel) onCancel();
            }
        };

        document.body.appendChild(dialog);
    }
}
