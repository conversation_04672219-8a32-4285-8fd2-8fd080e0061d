{"plugin": {"name": "图层智能分离器", "version": "1.0.0", "author": "AI Assistant", "description": "AI驱动的图片文字与元素分层工具"}, "ocr": {"defaultLanguage": "chi_sim+eng", "defaultAccuracy": "balanced", "supportedLanguages": [{"code": "chi_sim+eng", "name": "中英文", "description": "同时识别中文和英文"}, {"code": "chi_sim", "name": "简体中文", "description": "仅识别简体中文"}, {"code": "chi_tra", "name": "繁体中文", "description": "仅识别繁体中文"}, {"code": "eng", "name": "英文", "description": "仅识别英文"}, {"code": "jpn", "name": "日文", "description": "识别日文（平假名、片假名、汉字）"}, {"code": "kor", "name": "韩文", "description": "识别韩文（한글）"}, {"code": "rus", "name": "俄文", "description": "识别俄文（кириллица）"}], "accuracyModes": [{"code": "fast", "name": "快速", "description": "快速识别，适合预览", "confidence": 70}, {"code": "balanced", "name": "平衡", "description": "速度和精度的平衡", "confidence": 80}, {"code": "accurate", "name": "精确", "description": "最高精度，处理时间较长", "confidence": 90}]}, "segmentation": {"defaultMode": "auto", "defaultQuality": "standard", "modes": [{"code": "auto", "name": "自动分离", "description": "智能检测并分离前景元素"}, {"code": "person", "name": "人物分离", "description": "专门针对人物照片优化"}, {"code": "object", "name": "物体分离", "description": "适用于产品图片等物体分离"}], "qualityLevels": [{"code": "draft", "name": "草稿", "description": "快速处理，适合预览效果"}, {"code": "standard", "name": "标准", "description": "推荐设置，平衡质量和速度"}, {"code": "high", "name": "高质量", "description": "最佳效果，处理时间较长"}]}, "api": {"removeBg": {"endpoint": "https://api.remove.bg/v1.0/removebg", "accountEndpoint": "https://api.remove.bg/v1.0/account", "maxFileSize": "12MB", "supportedFormats": ["jpg", "jpeg", "png"]}, "tesseract": {"workerPath": "https://unpkg.com/tesseract.js@4/dist/worker.min.js", "corePath": "https://unpkg.com/tesseract.js-core@4/tesseract-core.wasm.js"}}, "ui": {"theme": {"primaryColor": "#0066cc", "secondaryColor": "#666666", "successColor": "#00aa00", "warningColor": "#ff9900", "errorColor": "#cc0000"}, "animations": {"fadeInDuration": 300, "progressUpdateDuration": 300, "messageDisplayDuration": 5000}, "layout": {"maxWidth": "400px", "spacing": {"xs": "4px", "sm": "8px", "md": "16px", "lg": "24px"}}}, "performance": {"maxConcurrentOperations": 1, "timeoutDuration": 300000, "memoryLimit": "512MB", "cacheSize": "100MB"}, "defaults": {"preserveOriginalLayer": true, "autoSaveSettings": true, "showProgressDetails": true, "enableBatchProcessing": true, "maxBatchSize": 10}}