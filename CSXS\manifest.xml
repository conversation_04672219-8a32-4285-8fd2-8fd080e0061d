<?xml version="1.0" encoding="UTF-8"?>
<ExtensionManifest Version="9.0" ExtensionBundleId="com.ai.ps-layer-separator" ExtensionBundleVersion="1.0.0" ExtensionBundleName="图层智能分离器" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Author>AI Assistant</Author>
  <Contact><EMAIL></Contact>
  <Legal>Copyright 2024</Legal>
  <Abstract>AI驱动的图片文字与元素分层工具</Abstract>
  
  <ExtensionList>
    <Extension Id="com.ai.ps-layer-separator.panel" Version="1.0.0"/>
  </ExtensionList>
  
  <ExecutionEnvironment>
    <HostList>
      <Host Name="PHXS" Version="[15.0,99.9]"/>
      <Host Name="PHSP" Version="[15.0,99.9]"/>
    </HostList>
    <LocaleList>
      <Locale Code="All"/>
    </LocaleList>
    <RequiredRuntimeList>
      <RequiredRuntime Name="CSXS" Version="9.0"/>
    </RequiredRuntimeList>
  </ExecutionEnvironment>
  
  <DispatchInfoList>
    <Extension Id="com.ai.ps-layer-separator.panel">
      <DispatchInfo>
        <Resources>
          <MainPath>./index.html</MainPath>
          <ScriptPath>./jsx/host.jsx</ScriptPath>
          <CEFCommandLine>
            <Parameter>--allow-file-access</Parameter>
            <Parameter>--allow-file-access-from-files</Parameter>
            <Parameter>--enable-nodejs</Parameter>
            <Parameter>--mixed-context</Parameter>
          </CEFCommandLine>
        </Resources>
        <Lifecycle>
          <AutoVisible>true</AutoVisible>
        </Lifecycle>
        <UI>
          <Type>Panel</Type>
          <Menu>图层智能分离器</Menu>
          <Geometry>
            <Size>
              <Width>350</Width>
              <Height>600</Height>
            </Size>
            <MinSize>
              <Width>300</Width>
              <Height>400</Height>
            </MinSize>
            <MaxSize>
              <Width>500</Width>
              <Height>800</Height>
            </MaxSize>
          </Geometry>
          <Icons>
            <Icon Type="Normal">./assets/icons/icon-23.png</Icon>
            <Icon Type="RollOver">./assets/icons/icon-23.png</Icon>
            <Icon Type="DarkNormal">./assets/icons/icon-23.png</Icon>
            <Icon Type="DarkRollOver">./assets/icons/icon-23.png</Icon>
          </Icons>
        </UI>
      </DispatchInfo>
    </Extension>
  </DispatchInfoList>
</ExtensionManifest>
