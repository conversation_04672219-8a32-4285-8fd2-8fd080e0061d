/**
 * Photoshop API助手类 - 封装常用的Photoshop操作
 */
class PSAPIHelper {
    constructor() {
        this.app = require('photoshop').app;
        this.core = require('photoshop').core;
    }

    /**
     * 获取当前活动文档
     */
    async getActiveDocument() {
        try {
            if (this.app.documents.length === 0) {
                throw new Error('没有打开的文档');
            }
            return this.app.activeDocument;
        } catch (error) {
            throw new Error('获取活动文档失败: ' + error.message);
        }
    }

    /**
     * 获取当前活动图层
     */
    async getActiveLayer() {
        try {
            const doc = await this.getActiveDocument();
            if (!doc.activeLayer) {
                throw new Error('没有选中的图层');
            }
            return doc.activeLayer;
        } catch (error) {
            throw new Error('获取活动图层失败: ' + error.message);
        }
    }

    /**
     * 获取选中的图层列表
     */
    async getSelectedLayers() {
        try {
            const doc = await this.getActiveDocument();
            
            // 使用batchPlay获取选中的图层
            const result = await this.core.executeAsModal(async () => {
                return await this.core.batchPlay([
                    {
                        "_obj": "get",
                        "_target": [
                            {
                                "_property": "targetLayers"
                            },
                            {
                                "_ref": "document",
                                "_id": doc.id
                            }
                        ]
                    }
                ], {});
            });

            if (result && result[0] && result[0].targetLayers) {
                const layerIds = result[0].targetLayers.map(layer => layer._id);
                return layerIds.map(id => this.getLayerById(doc, id)).filter(Boolean);
            }

            // 如果没有多选图层，返回当前活动图层
            return [doc.activeLayer];

        } catch (error) {
            console.warn('获取选中图层失败，使用活动图层:', error);
            const activeLayer = await this.getActiveLayer();
            return activeLayer ? [activeLayer] : [];
        }
    }

    /**
     * 根据ID获取图层
     */
    getLayerById(document, layerId) {
        try {
            // 递归搜索图层
            const findLayer = (layers) => {
                for (const layer of layers) {
                    if (layer.id === layerId) {
                        return layer;
                    }
                    if (layer.layers && layer.layers.length > 0) {
                        const found = findLayer(layer.layers);
                        if (found) return found;
                    }
                }
                return null;
            };

            return findLayer(document.layers);
        } catch (error) {
            console.error('根据ID获取图层失败:', error);
            return null;
        }
    }

    /**
     * 创建新图层
     */
    async createLayer(name, type = 'normal') {
        try {
            const doc = await this.getActiveDocument();
            
            return await this.core.executeAsModal(async () => {
                const result = await this.core.batchPlay([
                    {
                        "_obj": "make",
                        "_target": [
                            {
                                "_ref": "layer"
                            }
                        ],
                        "name": name,
                        "mode": {
                            "_enum": "blendMode",
                            "_value": "normal"
                        }
                    }
                ], {});

                return doc.activeLayer;
            });

        } catch (error) {
            throw new Error('创建图层失败: ' + error.message);
        }
    }

    /**
     * 创建文字图层
     */
    async createTextLayer(text, options = {}) {
        try {
            const {
                x = 0,
                y = 0,
                fontSize = 12,
                fontFamily = 'Arial',
                color = { r: 0, g: 0, b: 0 },
                name = '文字图层'
            } = options;

            return await this.core.executeAsModal(async () => {
                const result = await this.core.batchPlay([
                    {
                        "_obj": "make",
                        "_target": [
                            {
                                "_ref": "textLayer"
                            }
                        ],
                        "textKey": {
                            "_obj": "textLayer",
                            "textKey": text,
                            "textStyleRange": [
                                {
                                    "_obj": "textStyleRange",
                                    "from": 0,
                                    "to": text.length,
                                    "textStyle": {
                                        "_obj": "textStyle",
                                        "fontName": fontFamily,
                                        "size": {
                                            "_unit": "pointsUnit",
                                            "_value": fontSize
                                        },
                                        "color": {
                                            "_obj": "RGBColor",
                                            "red": color.r,
                                            "grain": color.g,
                                            "blue": color.b
                                        }
                                    }
                                }
                            ],
                            "paragraphStyleRange": [
                                {
                                    "_obj": "paragraphStyleRange",
                                    "from": 0,
                                    "to": text.length,
                                    "paragraphStyle": {
                                        "_obj": "paragraphStyle"
                                    }
                                }
                            ]
                        },
                        "name": name
                    }
                ], {});

                // 设置位置
                if (x !== 0 || y !== 0) {
                    await this.core.batchPlay([
                        {
                            "_obj": "move",
                            "_target": [
                                {
                                    "_ref": "layer",
                                    "_enum": "ordinal",
                                    "_value": "targetEnum"
                                }
                            ],
                            "to": {
                                "_obj": "offset",
                                "horizontal": {
                                    "_unit": "pixelsUnit",
                                    "_value": x
                                },
                                "vertical": {
                                    "_unit": "pixelsUnit",
                                    "_value": y
                                }
                            }
                        }
                    ], {});
                }

                const doc = await this.getActiveDocument();
                return doc.activeLayer;
            });

        } catch (error) {
            throw new Error('创建文字图层失败: ' + error.message);
        }
    }

    /**
     * 复制图层
     */
    async duplicateLayer(layer, name = null) {
        try {
            return await this.core.executeAsModal(async () => {
                const duplicateName = name || (layer.name + '_副本');
                
                await this.core.batchPlay([
                    {
                        "_obj": "duplicate",
                        "_target": [
                            {
                                "_ref": "layer",
                                "_id": layer.id
                            }
                        ],
                        "name": duplicateName
                    }
                ], {});

                const doc = await this.getActiveDocument();
                return doc.activeLayer;
            });

        } catch (error) {
            throw new Error('复制图层失败: ' + error.message);
        }
    }

    /**
     * 删除图层
     */
    async deleteLayer(layer) {
        try {
            return await this.core.executeAsModal(async () => {
                await this.core.batchPlay([
                    {
                        "_obj": "delete",
                        "_target": [
                            {
                                "_ref": "layer",
                                "_id": layer.id
                            }
                        ]
                    }
                ], {});
            });

        } catch (error) {
            throw new Error('删除图层失败: ' + error.message);
        }
    }

    /**
     * 导出图层为图像
     */
    async exportLayerAsImage(layer, format = 'png', options = {}) {
        try {
            return await this.core.executeAsModal(async () => {
                // 选择图层
                await this.selectLayer(layer);

                // 复制图层内容
                await this.core.batchPlay([
                    {
                        "_obj": "copyToLayer"
                    }
                ], {});

                // 导出选项
                const exportOptions = {
                    "_obj": "export",
                    "_target": [
                        {
                            "_ref": "layer",
                            "_enum": "ordinal",
                            "_value": "targetEnum"
                        }
                    ],
                    "as": {
                        "_obj": format === 'png' ? "PNGFormat" : "JPEGFormat",
                        ...options
                    }
                };

                const result = await this.core.batchPlay([exportOptions], {});
                return result;
            });

        } catch (error) {
            throw new Error('导出图层失败: ' + error.message);
        }
    }

    /**
     * 选择图层
     */
    async selectLayer(layer) {
        try {
            await this.core.batchPlay([
                {
                    "_obj": "select",
                    "_target": [
                        {
                            "_ref": "layer",
                            "_id": layer.id
                        }
                    ],
                    "makeVisible": false
                }
            ], {});
        } catch (error) {
            throw new Error('选择图层失败: ' + error.message);
        }
    }

    /**
     * 应用蒙版到图层
     */
    async applyMaskToLayer(layer, maskData) {
        try {
            return await this.core.executeAsModal(async () => {
                // 选择图层
                await this.selectLayer(layer);

                // 创建蒙版
                await this.core.batchPlay([
                    {
                        "_obj": "make",
                        "_target": [
                            {
                                "_ref": "channel"
                            }
                        ],
                        "at": {
                            "_ref": "channel",
                            "_enum": "ordinal",
                            "_value": "targetEnum"
                        },
                        "using": {
                            "_enum": "userMaskEnabled",
                            "_value": "revealAll"
                        }
                    }
                ], {});

                // 这里需要将maskData应用到蒙版
                // 具体实现取决于maskData的格式

                return layer;
            });

        } catch (error) {
            throw new Error('应用蒙版失败: ' + error.message);
        }
    }

    /**
     * 获取图层边界
     */
    async getLayerBounds(layer) {
        try {
            const result = await this.core.batchPlay([
                {
                    "_obj": "get",
                    "_target": [
                        {
                            "_property": "bounds"
                        },
                        {
                            "_ref": "layer",
                            "_id": layer.id
                        }
                    ]
                }
            ], {});

            if (result && result[0] && result[0].bounds) {
                const bounds = result[0].bounds;
                return {
                    left: bounds.left._value,
                    top: bounds.top._value,
                    right: bounds.right._value,
                    bottom: bounds.bottom._value,
                    width: bounds.right._value - bounds.left._value,
                    height: bounds.bottom._value - bounds.top._value
                };
            }

            return null;

        } catch (error) {
            throw new Error('获取图层边界失败: ' + error.message);
        }
    }

    /**
     * 检查图层类型
     */
    isTextLayer(layer) {
        return layer.kind === 'text';
    }

    /**
     * 检查图层是否可见
     */
    isLayerVisible(layer) {
        return layer.visible;
    }

    /**
     * 设置图层可见性
     */
    async setLayerVisibility(layer, visible) {
        try {
            await this.core.batchPlay([
                {
                    "_obj": "set",
                    "_target": [
                        {
                            "_ref": "layer",
                            "_id": layer.id
                        }
                    ],
                    "to": {
                        "_obj": "layer",
                        "visible": visible
                    }
                }
            ], {});
        } catch (error) {
            throw new Error('设置图层可见性失败: ' + error.message);
        }
    }
}
