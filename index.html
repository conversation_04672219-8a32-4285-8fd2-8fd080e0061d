<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图层智能分离器</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <!-- 头部标题 -->
        <header class="header">
            <h1>图层智能分离器</h1>
            <p class="subtitle">AI驱动的图片文字与元素分层工具</p>
        </header>

        <!-- 主要功能区域 -->
        <main class="main-content">
            <!-- 文字分层功能 -->
            <section class="feature-section">
                <div class="feature-header">
                    <h2>📝 文字分层</h2>
                    <p>智能识别并提取图片中的文字，保持原始属性</p>
                </div>
                <div class="feature-controls">
                    <div class="language-selector">
                        <label for="ocrLanguage">识别语言:</label>
                        <select id="ocrLanguage">
                            <option value="chi_sim+eng">中英文</option>
                            <option value="chi_sim">简体中文</option>
                            <option value="chi_tra">繁体中文</option>
                            <option value="eng">英文</option>
                            <option value="jpn">日文</option>
                            <option value="kor">韩文</option>
                            <option value="rus">俄文</option>
                        </select>
                    </div>
                    <button id="extractTextBtn" class="primary-btn">
                        <span class="btn-icon">🔍</span>
                        提取文字分层
                    </button>
                </div>
            </section>

            <!-- 元素分层功能 -->
            <section class="feature-section">
                <div class="feature-header">
                    <h2>🎨 元素分层</h2>
                    <p>智能分离图片元素和背景，实现透明化处理</p>
                </div>
                <div class="feature-controls">
                    <div class="segmentation-options">
                        <label>
                            <input type="radio" name="segmentMode" value="auto" checked>
                            自动分离
                        </label>
                        <label>
                            <input type="radio" name="segmentMode" value="person">
                            人物分离
                        </label>
                        <label>
                            <input type="radio" name="segmentMode" value="object">
                            物体分离
                        </label>
                    </div>
                    <button id="separateElementBtn" class="primary-btn">
                        <span class="btn-icon">✂️</span>
                        分离元素背景
                    </button>
                </div>
            </section>

            <!-- 批量处理功能 -->
            <section class="feature-section">
                <div class="feature-header">
                    <h2>⚡ 批量处理</h2>
                    <p>同时处理多个图层，提高工作效率</p>
                </div>
                <div class="feature-controls">
                    <div class="batch-options">
                        <label>
                            <input type="checkbox" id="batchText">
                            批量文字分层
                        </label>
                        <label>
                            <input type="checkbox" id="batchElement">
                            批量元素分离
                        </label>
                    </div>
                    <button id="batchProcessBtn" class="secondary-btn">
                        <span class="btn-icon">🔄</span>
                        批量处理选中图层
                    </button>
                </div>
            </section>
        </main>

        <!-- 进度显示区域 -->
        <div id="progressContainer" class="progress-container hidden">
            <div class="progress-header">
                <h3 id="progressTitle">处理中...</h3>
                <button id="cancelBtn" class="cancel-btn">取消</button>
            </div>
            <div class="progress-bar">
                <div id="progressFill" class="progress-fill"></div>
            </div>
            <p id="progressText" class="progress-text">准备中...</p>
        </div>

        <!-- 结果显示区域 -->
        <div id="resultContainer" class="result-container hidden">
            <div class="result-header">
                <h3>处理结果</h3>
                <button id="closeResultBtn" class="close-btn">×</button>
            </div>
            <div id="resultContent" class="result-content">
                <!-- 动态生成结果内容 -->
            </div>
        </div>

        <!-- 设置区域 -->
        <footer class="settings">
            <details>
                <summary>高级设置</summary>
                <div class="settings-content">
                    <div class="setting-item">
                        <label for="ocrAccuracy">OCR精度:</label>
                        <select id="ocrAccuracy">
                            <option value="fast">快速</option>
                            <option value="balanced" selected>平衡</option>
                            <option value="accurate">精确</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label for="segmentQuality">分离质量:</label>
                        <select id="segmentQuality">
                            <option value="draft">草稿</option>
                            <option value="standard" selected>标准</option>
                            <option value="high">高质量</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label>
                            <input type="checkbox" id="preserveOriginal" checked>
                            保留原始图层
                        </label>
                    </div>
                </div>
            </details>
        </footer>
    </div>

    <!-- 加载第三方库 -->
    <script src="https://unpkg.com/tesseract.js@4/dist/tesseract.min.js"></script>

    <!-- 加载自定义类库 -->
    <script src="lib/ps-api-helper.js"></script>
    <script src="lib/ui-controller.js"></script>
    <script src="lib/text-extractor.js"></script>
    <script src="lib/image-segmentation.js"></script>

    <!-- 主要脚本文件 -->
    <script src="index.js"></script>
</body>
</html>
